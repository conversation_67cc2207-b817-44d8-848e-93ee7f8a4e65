#!/usr/bin/env node

/**
 * Test script to verify the sharing fix works
 * This script will test the /api/charts/share/:shareId endpoint
 */

const http = require('http');

// Configuration
const HOST = 'localhost';
const PORT = 3000;

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: HOST,
      port: PORT,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testSharing() {
  console.log('🧪 Testing diagram sharing functionality...\n');

  try {
    // Test 1: Check if server is running
    console.log('1. Testing server connectivity...');
    const debugResponse = await makeRequest('/api/debug');
    
    if (debugResponse.statusCode === 200) {
      console.log('✅ Server is running');
      console.log(`   Environment: ${debugResponse.body.environment}`);
    } else {
      console.log('❌ Server is not responding properly');
      console.log(`   Status: ${debugResponse.statusCode}`);
      return;
    }

    // Test 2: Try to access a shared chart (this will likely fail with 404, but we want to see the error)
    console.log('\n2. Testing shared chart access...');
    const testShareId = 'test123456'; // This probably doesn't exist, but we want to test the endpoint
    const shareResponse = await makeRequest(`/api/charts/share/${testShareId}`);
    
    console.log(`   Status Code: ${shareResponse.statusCode}`);
    console.log(`   Response: ${JSON.stringify(shareResponse.body, null, 2)}`);
    
    if (shareResponse.statusCode === 404) {
      console.log('✅ Endpoint is working (404 is expected for non-existent share ID)');
    } else if (shareResponse.statusCode === 403) {
      console.log('❌ Still getting 403 Forbidden - the fix may not be applied');
    } else if (shareResponse.statusCode === 200) {
      console.log('✅ Shared chart loaded successfully!');
    } else {
      console.log(`⚠️  Unexpected status code: ${shareResponse.statusCode}`);
    }

    // Test 3: Check if we can access the charts endpoint at all
    console.log('\n3. Testing charts endpoint...');
    const chartsResponse = await makeRequest('/api/charts');
    console.log(`   Status Code: ${chartsResponse.statusCode}`);
    
    if (chartsResponse.statusCode === 401) {
      console.log('✅ Charts endpoint requires authentication (expected)');
    } else {
      console.log(`   Response: ${JSON.stringify(chartsResponse.body, null, 2)}`);
    }

  } catch (error) {
    console.error('❌ Error during testing:', error.message);
    console.log('\n💡 Make sure the server is running with: npm start');
  }
}

// Run the test
if (require.main === module) {
  testSharing().then(() => {
    console.log('\n🏁 Test completed');
  });
}

module.exports = { testSharing };
