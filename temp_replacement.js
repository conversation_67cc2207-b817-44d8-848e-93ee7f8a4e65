// Get a chart by share ID - FIXED VERSION
exports.getChartByShareId = async (req, res) => {
  try {
    console.log('Getting shared chart with ID:', req.params.shareId);
    console.log('Session user:', req.session?.user?.id);
    
    const chart = await Chart.findByShareId(req.params.shareId);

    if (!chart) {
      console.log('Chart not found for share ID:', req.params.shareId);
      return res.status(404).json({ message: 'Chart not found' });
    }

    console.log('Found chart:', {
      id: chart.id,
      title: chart.title,
      public: chart.public,
      user_id: chart.user_id,
      share_id: chart.share_id
    });

    // FIXED: Allow access to any chart via share ID
    // The share ID itself acts as the authorization mechanism
    // If someone has the share ID, they should be able to view the chart
    // regardless of whether it's marked as "public" or not
    
    res.json({ chart });
  } catch (error) {
    console.error('Get shared chart error:', error);
    res.status(500).json({ message: 'Server error while fetching shared chart' });
  }
};
