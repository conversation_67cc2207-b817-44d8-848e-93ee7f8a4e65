#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Copy the fixed file to the correct location
const sourceFile = path.join(__dirname, 'backend/controllers/chartController-fixed.js');
const targetFile = path.join(__dirname, 'backend/controllers/chartController.js');

try {
  // Read the source file
  const content = fs.readFileSync(sourceFile, 'utf8');
  
  // Write to target file (this will overwrite if it exists)
  fs.writeFileSync(targetFile, content);
  
  console.log('✅ Successfully copied the fixed chartController.js');
  console.log('The sharing issue should now be resolved.');
  
  // Verify the fix is in place
  if (content.includes('FIXED: Allow access to any chart via share ID')) {
    console.log('✅ Fix verified: Share ID authorization logic is updated');
  } else {
    console.log('❌ Fix not found in the copied file');
  }
  
} catch (error) {
  console.error('❌ Error copying file:', error.message);
}
